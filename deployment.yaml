apiVersion: apps/v1
kind: Deployment
metadata:
  name: demo-app-sabeco
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: demo-app-sabeco
  template:
    metadata:
      labels:
        app: demo-app-sabeco
    spec:
      containers:
        - name: demo-app-sabeco
          image: sabecocontainerThinh.azurecr.io/demo-app-sabeco:sabeco-test-8
          ports:
            - containerPort: 5000
          env:
            - name: AZURE_CLIENT_ID
              value: "2d3baa0e-7dbf-4fc3-8c29-4cf1160801be"
            - name: KEYVAULT_URL
              value: "sabeco-kv-thinh.vault.azure.net"
            - name: SQL_SERVER
              value: "sabeco-fo-group.database.windows.net"
            - name: SQL_DB
              value: "sabeco-sql-db"
            - name: SQL_USER
              value: "lvthinh"
            - name: SQL_PWD
              value: "Tatn25ct10lt@"
            - name: COSMOS_ENDPOINT
              value: "cosmosdb-thinh.documents.azure.com"
            - name: COSMOS_KEY
              value: "****************************************************************************************"
            - name: BLOB_URL
              value: "sabecostorageaccount.blob.core.windows.net"
            - name: ACR_NAME
              value: "sabecocontainerThinh"
            - name: ACR_SUBSCRIPTION
              value: "ea82a715-518a-4560-9e70-8f2e2e635a5f"
            - name: ACR_RG
              value: "THINH"
            - name: REDIS_CONN
              value: "rediss://:yaZ1MlvYDDVlkRbPfOjhT6ihiMYF6HUSaAzCaAOnCLg=@sabeco-redis-2.redis.cache.windows.net:6380"